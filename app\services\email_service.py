"""
Email service for sending notifications and alerts.
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from app.core.config import get_settings
from app.core.logging import app_logger


class EmailService:
    """Service for sending email notifications."""
    
    def __init__(self):
        self.settings = get_settings()
        self.smtp_host = getattr(self.settings, 'smtp_host', None)
        self.smtp_port = getattr(self.settings, 'smtp_port', 587)
        self.smtp_user = getattr(self.settings, 'smtp_user', None)
        self.smtp_password = getattr(self.settings, 'smtp_password', None)
        self.from_email = getattr(self.settings, 'from_email', self.smtp_user)
        self.alert_recipients = self._parse_recipients(
            getattr(self.settings, 'alert_email_recipients', '')
        )
    
    def _parse_recipients(self, recipients_str: str) -> List[str]:
        """Parse comma-separated email recipients."""
        if not recipients_str:
            return []
        return [email.strip() for email in recipients_str.split(',') if email.strip()]
    
    def is_configured(self) -> bool:
        """Check if email service is properly configured."""
        return all([
            self.smtp_host,
            self.smtp_user,
            self.smtp_password,
            self.from_email
        ])
    
    async def send_alert_email(self, alert) -> bool:
        """Send alert notification email."""
        if not self.is_configured() or not self.alert_recipients:
            app_logger.warning("Email service not configured or no recipients specified")
            return False
        
        try:
            subject = f"🚨 Production Alert: {alert.title}"
            body = self._generate_alert_email_body(alert)
            
            return await self.send_email(
                recipients=self.alert_recipients,
                subject=subject,
                body=body,
                is_html=True
            )
            
        except Exception as e:
            app_logger.error(f"Failed to send alert email: {e}", exc_info=True)
            return False
    
    def _generate_alert_email_body(self, alert) -> str:
        """Generate HTML email body for alert."""
        severity_colors = {
            "low": "#28a745",
            "medium": "#ffc107", 
            "high": "#fd7e14",
            "critical": "#dc3545"
        }
        
        severity_color = severity_colors.get(alert.severity.value, "#6c757d")
        
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Production Alert</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ background-color: {severity_color}; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .alert-info {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }}
                .footer {{ background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #6c757d; }}
                .severity {{ display: inline-block; padding: 4px 8px; border-radius: 4px; color: white; background-color: {severity_color}; font-weight: bold; text-transform: uppercase; }}
                .data-table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                .data-table th, .data-table td {{ border: 1px solid #dee2e6; padding: 8px; text-align: left; }}
                .data-table th {{ background-color: #f8f9fa; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚨 Production Alert</h1>
                    <h2>{alert.title}</h2>
                </div>
                
                <div class="content">
                    <div class="alert-info">
                        <p><strong>Severity:</strong> <span class="severity">{alert.severity.value}</span></p>
                        <p><strong>Type:</strong> {alert.type.value.replace('_', ' ').title()}</p>
                        <p><strong>Time:</strong> {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                        <p><strong>Alert ID:</strong> {alert.id}</p>
                    </div>
                    
                    <h3>Description</h3>
                    <p>{alert.message}</p>
                    
                    {self._format_alert_data(alert.data)}
                    
                    <div class="alert-info">
                        <p><strong>Recommended Actions:</strong></p>
                        <ul>
                            {self._get_recommended_actions(alert)}
                        </ul>
                    </div>
                </div>
                
                <div class="footer">
                    <p>This alert was generated automatically by the Excalibur ERP Production Monitoring System.</p>
                    <p>Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_body
    
    def _format_alert_data(self, data: Dict[str, Any]) -> str:
        """Format alert data as HTML table."""
        if not data:
            return ""
        
        html = "<h3>Alert Details</h3><table class='data-table'>"
        for key, value in data.items():
            formatted_key = key.replace('_', ' ').title()
            html += f"<tr><th>{formatted_key}</th><td>{value}</td></tr>"
        html += "</table>"
        
        return html
    
    def _get_recommended_actions(self, alert) -> str:
        """Get recommended actions based on alert type."""
        actions = {
            "production_delay": [
                "Review the production schedule for the affected order",
                "Check for resource availability and bottlenecks",
                "Contact the production team to assess the situation",
                "Consider adjusting priorities or reallocating resources"
            ],
            "system_error": [
                "Check system logs for detailed error information",
                "Verify database connectivity and system resources",
                "Contact the IT support team if the issue persists",
                "Monitor system performance closely"
            ],
            "database_connection": [
                "Check database server status and connectivity",
                "Verify network connectivity to the database server",
                "Review database server logs for errors",
                "Contact the database administrator immediately"
            ],
            "performance_degradation": [
                "Monitor system resources (CPU, memory, disk)",
                "Check for long-running queries or processes",
                "Review recent system changes or deployments",
                "Consider scaling resources if needed"
            ],
            "threshold_exceeded": [
                "Review the specific metrics that exceeded thresholds",
                "Analyze trends to identify root causes",
                "Adjust processes or resources as needed",
                "Update alert thresholds if appropriate"
            ]
        }
        
        alert_actions = actions.get(alert.type.value, [
            "Review the alert details carefully",
            "Take appropriate corrective action",
            "Monitor the situation closely",
            "Contact support if needed"
        ])
        
        return "".join(f"<li>{action}</li>" for action in alert_actions)
    
    async def send_email(self, recipients: List[str], subject: str, body: str, 
                        is_html: bool = False, attachments: Optional[List[str]] = None) -> bool:
        """Send email to specified recipients."""
        if not self.is_configured():
            app_logger.error("Email service not configured")
            return False
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = subject
            
            # Add body
            msg.attach(MIMEText(body, 'html' if is_html else 'plain', 'utf-8'))
            
            # Add attachments if provided
            if attachments:
                for file_path in attachments:
                    if Path(file_path).exists():
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                            encoders.encode_base64(part)
                            part.add_header(
                                'Content-Disposition',
                                f'attachment; filename= {Path(file_path).name}'
                            )
                            msg.attach(part)
            
            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.smtp_user, self.smtp_password)
                server.send_message(msg)
            
            app_logger.info(f"Email sent successfully to {len(recipients)} recipients")
            return True
            
        except Exception as e:
            app_logger.error(f"Failed to send email: {e}", exc_info=True)
            return False
    
    async def send_system_status_report(self, status_data: Dict[str, Any]) -> bool:
        """Send system status report email."""
        if not self.is_configured() or not self.alert_recipients:
            return False
        
        try:
            subject = f"📊 System Status Report - {datetime.now().strftime('%Y-%m-%d')}"
            body = self._generate_status_report_body(status_data)
            
            return await self.send_email(
                recipients=self.alert_recipients,
                subject=subject,
                body=body,
                is_html=True
            )
            
        except Exception as e:
            app_logger.error(f"Failed to send status report: {e}", exc_info=True)
            return False
    
    def _generate_status_report_body(self, status_data: Dict[str, Any]) -> str:
        """Generate HTML body for status report."""
        # Implementation for status report email body
        # This would be similar to alert email but formatted for status reports
        return f"""
        <html>
        <body>
            <h2>System Status Report</h2>
            <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <pre>{status_data}</pre>
        </body>
        </html>
        """
