#!/usr/bin/env python3
"""
FastAPI Application for Production Time Tracking - Excalibur ERP
Refactored from Streamlit to FastAPI for better scalability and hosting flexibility.
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pathlib import Path
from contextlib import asynccontextmanager

# Import our modular components
from app.core.config import get_settings
from app.core.database import init_analyzer, cleanup_analyzer
from app.core.logging import setup_logging, app_logger
from app.core.scheduler import scheduler
from app.middleware.logging_middleware import LoggingMiddleware
from app.routes import (
    dashboard_routes,
    of_routes,
    health_routes,
    alert_routes
)

# Global analyzer instance (will be initialized on startup)
analyzer = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize the application on startup."""
    global analyzer

    # Setup logging
    setup_logging()
    app_logger.info("Starting FastAPI Production Monitoring Application")

    try:
        analyzer = init_analyzer()
        app_logger.info("✅ Data analyzer initialized successfully")
    except Exception as e:
        app_logger.error(f"❌ Failed to initialize data analyzer: {e}", exc_info=True)
        analyzer = None

    # Start background scheduler
    try:
        await scheduler.start()
        app_logger.info("✅ Background scheduler started successfully")
    except Exception as e:
        app_logger.error(f"❌ Failed to start background scheduler: {e}", exc_info=True)

    yield

    # Cleanup on shutdown
    app_logger.info("Shutting down application")

    # Stop background scheduler
    try:
        await scheduler.stop()
        app_logger.info("✅ Background scheduler stopped")
    except Exception as e:
        app_logger.error(f"❌ Error stopping background scheduler: {e}", exc_info=True)

    cleanup_analyzer()

# Create FastAPI app with lifespan
settings = get_settings()
app = FastAPI(
    title=settings.app_name,
    description=settings.app_description,
    version=settings.app_version,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(LoggingMiddleware)

# Setup static files and templates
static_dir = Path("static")
templates_dir = Path("templates")

# Create directories if they don't exist
static_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Include route modules
app.include_router(dashboard_routes.router)
app.include_router(of_routes.router)
app.include_router(health_routes.router)
app.include_router(alert_routes.router)

# All routes are now handled by the modular route system above

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="localhost", port=8000, reload=True)
