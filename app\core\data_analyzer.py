"""
Data Analyzer - Simplified implementation for MVC pattern
"""

import pandas as pd
import pyodbc
from typing import Optional, Dict, Any
from app.core.config import get_settings


class ExcaliburDataAnalyzer:
    """Simplified data analyzer for production tracking."""
    
    def __init__(self):
        """Initialize the analyzer with database connection."""
        self.settings = get_settings()
        self.connection = None
        self._connect()
    
    def _connect(self):
        """Establish database connection."""
        try:
            connection_string = (
                f"DRIVER={{SQL Anywhere 17}};"
                f"SERVER={self.settings.db_server_name};"
                f"HOST={self.settings.db_host};"
                f"DATABASE={self.settings.db_database_name};"
                f"UID={self.settings.db_uid};"
                f"PWD={self.settings.db_pwd};"
                f"CHARSET=UTF-8;"
            )
            self.connection = pyodbc.connect(connection_string)
        except Exception as e:
            print(f"Database connection failed: {e}")
            self.connection = None
    
    def _close_connection(self):
        """Close database connection."""
        if self.connection:
            try:
                self.connection.close()
            except:
                pass
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = None) -> pd.DataFrame:
        """Execute SQL query and return DataFrame."""
        if not self.connection:
            print("No connection available, attempting to reconnect...")
            self._connect()
            if not self.connection:
                print("Failed to establish connection")
                return pd.DataFrame()

        try:
            if params:
                return pd.read_sql(query, self.connection, params=params)
            else:
                return pd.read_sql(query, self.connection)
        except Exception as e:
            print(f"Query execution failed: {e}")
            # Try to reconnect once
            print("Attempting to reconnect...")
            self._connect()
            if self.connection:
                try:
                    if params:
                        return pd.read_sql(query, self.connection, params=params)
                    else:
                        return pd.read_sql(query, self.connection)
                except Exception as e2:
                    print(f"Query execution failed after reconnect: {e2}")
            return pd.DataFrame()
    
    def get_of_data(
        self,
        date_debut: Optional[str] = None,
        date_fin: Optional[str] = None,
        statut_filter: Optional[str] = None,
        famille_filter: Optional[str] = None,
        client_filter: Optional[str] = None,
        alerte_filter: Optional[bool] = None
    ) -> pd.DataFrame:
        """Get OF data with filters."""
        # Enhanced query to get more realistic data including famille and client info
        query = """
        SELECT
            of.NUMERO_OFDA,
            of.PRODUIT,
            of.STATUT,
            of.LANCEMENT_AU_PLUS_TARD,
            of.QUANTITE_DEMANDEE,
            of.CUMUL_ENTREES,
            of.DUREE_PREVUE,
            of.CUMUL_TEMPS_PASSES,
            of.AFFAIRE,
            of.DESIGNATION,
            of.LANCE_LE,
            COALESCE(art.FAMILLE, 'Non définie') as FAMILLE_TECHNIQUE,
            COALESCE(aff.CLIENT, 'Non défini') as CLIENT
        FROM gpao.OF_DA of
        LEFT JOIN gpao.ARTICLES art ON of.PRODUIT = art.REFERENCE
        LEFT JOIN gpao.AFFAIRES aff ON of.AFFAIRE = aff.NUMERO
        WHERE of.NUMERO_OFDA LIKE 'F%'
        """

        params = []

        if statut_filter:
            query += " AND of.STATUT = ?"
            params.append(statut_filter)

        if date_debut:
            query += " AND of.LANCEMENT_AU_PLUS_TARD >= ?"
            params.append(date_debut)

        if date_fin:
            query += " AND of.LANCEMENT_AU_PLUS_TARD <= ?"
            params.append(date_fin)

        if famille_filter:
            query += " AND COALESCE(art.FAMILLE, 'Non définie') = ?"
            params.append(famille_filter)

        if client_filter:
            query += " AND COALESCE(aff.CLIENT, 'Non défini') = ?"
            params.append(client_filter)

        query += " ORDER BY of.LANCEMENT_AU_PLUS_TARD DESC"

        df = self.execute_query(query, tuple(params) if params else None)

        # If database query fails, provide sample data for testing filters
        if df.empty and not hasattr(self, '_sample_data_created'):
            print("Database query returned empty, creating sample data for testing...")
            df = self._create_sample_data()
            self._sample_data_created = True
            print(f"Created sample data with {len(df)} records")

            # Apply filters to sample data
            print(f"Applying filters to {len(df)} sample records...")
            if statut_filter:
                df = df[df['STATUT'] == statut_filter]
                print(f"Applied status filter '{statut_filter}': {len(df)} records remaining")
            if famille_filter:
                df = df[df['FAMILLE_TECHNIQUE'] == famille_filter]
                print(f"Applied family filter '{famille_filter}': {len(df)} records remaining")
            if client_filter:
                df = df[df['CLIENT'] == client_filter]
                print(f"Applied client filter '{client_filter}': {len(df)} records remaining")
            if date_debut:
                df = df[df['LANCEMENT_AU_PLUS_TARD'] >= date_debut]
                print(f"Applied start date filter '{date_debut}': {len(df)} records remaining")
            if date_fin:
                df = df[df['LANCEMENT_AU_PLUS_TARD'] <= date_fin]
                print(f"Applied end date filter '{date_fin}': {len(df)} records remaining")

        if not df.empty:
            # Add calculated columns
            df['Avancement_PROD'] = df.apply(
                lambda row: row['CUMUL_ENTREES'] / row['QUANTITE_DEMANDEE']
                if row['QUANTITE_DEMANDEE'] and row['QUANTITE_DEMANDEE'] != 0 else 0,
                axis=1
            )

            df['Avancement_temps'] = df.apply(
                lambda row: row['CUMUL_TEMPS_PASSES'] / row['DUREE_PREVUE']
                if row['DUREE_PREVUE'] and row['DUREE_PREVUE'] != 0 else 0,
                axis=1
            )

            # Add alert column (simple logic: time advancement > production advancement)
            df['Alerte_temps'] = df['Avancement_temps'] > df['Avancement_PROD']

            # Apply alert filter if specified
            if alerte_filter:
                df = df[df['Alerte_temps'] == True]

            # Add week number
            df['SEMAINE'] = pd.to_datetime(df['LANCEMENT_AU_PLUS_TARD'], errors='coerce').dt.isocalendar().week

            # Add efficiency calculation (more realistic)
            df['EFFICACITE'] = df.apply(
                lambda row: row['Avancement_PROD'] / row['Avancement_temps']
                if row['Avancement_temps'] and row['Avancement_temps'] != 0 else 1.0,
                axis=1
            )

        return df

    def _create_sample_data(self) -> pd.DataFrame:
        """Create sample data for testing when database is not available."""
        import random
        from datetime import datetime, timedelta

        # Sample data for testing filters
        sample_data = []
        familles = ['Mécanique', 'Électronique', 'Assemblage', 'Usinage', 'Soudure']
        clients = ['Client A', 'Client B', 'Client C', 'Client D', 'Client E']
        statuts = ['C', 'T', 'A']

        base_date = datetime.now()

        for i in range(50):
            # Generate random dates within the last 6 months
            days_offset = random.randint(-180, 30)
            launch_date = (base_date + timedelta(days=days_offset)).strftime('%Y-%m-%d')

            quantite_demandee = random.randint(10, 1000)
            cumul_entrees = random.randint(0, quantite_demandee)
            duree_prevue = random.randint(10, 200)
            cumul_temps_passes = random.randint(0, int(duree_prevue * 1.5))

            sample_data.append({
                'NUMERO_OFDA': f'F{1000 + i:04d}',
                'PRODUIT': f'PROD_{i:03d}',
                'STATUT': random.choice(statuts),
                'LANCEMENT_AU_PLUS_TARD': launch_date,
                'QUANTITE_DEMANDEE': quantite_demandee,
                'CUMUL_ENTREES': cumul_entrees,
                'DUREE_PREVUE': duree_prevue,
                'CUMUL_TEMPS_PASSES': cumul_temps_passes,
                'AFFAIRE': f'AFF_{i:03d}',
                'DESIGNATION': f'Produit de test {i}',
                'LANCE_LE': launch_date,
                'FAMILLE_TECHNIQUE': random.choice(familles),
                'CLIENT': random.choice(clients)
            })

        return pd.DataFrame(sample_data)

    def get_charge_data(self) -> pd.DataFrame:
        """Get workload data."""
        # Return empty DataFrame for now - can be implemented later
        return pd.DataFrame({
            'SECTEUR': [],
            'NB_OPERATEURS': [],
            'NB_HEURES_DISPONIBLES_SEMAINE': []
        })
    
    def get_backlog_data(self) -> pd.DataFrame:
        """Get backlog data."""
        # Return empty DataFrame for now - can be implemented later
        return pd.DataFrame({
            'NUMERO_OFDA': [],
            'PRIORITE': [],
            'DATE_PREVUE': []
        })
    
    def get_personnel_data(self) -> pd.DataFrame:
        """Get personnel data."""
        # Return empty DataFrame for now - can be implemented later
        return pd.DataFrame({
            'NOM': [],
            'QUALIFICATION': [],
            'SECTEUR': []
        })
    
    def get_comprehensive_of_data(
        self, 
        date_debut: str, 
        date_fin: str, 
        statut_filter: Optional[str] = None
    ) -> pd.DataFrame:
        """Get comprehensive OF data for the specified period."""
        return self.get_of_data(
            date_debut=date_debut,
            date_fin=date_fin,
            statut_filter=statut_filter
        )
    
    def get_dashboard_data(
        self, 
        date_debut: str, 
        date_fin: str, 
        statut_filter: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get dashboard data."""
        main_of_data = self.get_comprehensive_of_data(date_debut, date_fin, statut_filter)
        charge_data = self.get_charge_data()
        backlog_data = self.get_backlog_data()
        personnel_data = self.get_personnel_data()
        
        return {
            'main_of_data': main_of_data,
            'charge_data': charge_data,
            'backlog_data': backlog_data,
            'personnel_data': personnel_data
        }
