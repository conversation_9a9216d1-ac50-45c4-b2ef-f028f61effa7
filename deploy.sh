#!/bin/bash

# Production Deployment Script for FastAPI Production Monitoring
# Usage: ./deploy.sh [environment]
# Environment: dev (default) | prod

set -e  # Exit on any error

ENVIRONMENT=${1:-dev}
PROJECT_NAME="fastapi-production-tracker"

echo "🚀 Starting deployment for environment: $ENVIRONMENT"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please copy .env.template to .env and configure it."
    exit 1
fi

# Create logs directory
mkdir -p logs

# Function to deploy development environment
deploy_dev() {
    echo "📦 Building development environment..."
    
    # Stop existing containers
    docker-compose down --remove-orphans
    
    # Build and start services
    docker-compose up --build -d
    
    echo "✅ Development environment deployed successfully!"
    echo "📊 Dashboard: http://localhost:8000"
    echo "📚 API Docs: http://localhost:8000/docs"
    echo "🔍 Logs: docker-compose logs -f"
}

# Function to deploy production environment
deploy_prod() {
    echo "📦 Building production environment..."
    
    # Stop existing containers
    docker-compose -f docker-compose.prod.yml down --remove-orphans
    
    # Build and start services
    docker-compose -f docker-compose.prod.yml up --build -d
    
    # Wait for services to be ready
    echo "⏳ Waiting for services to be ready..."
    sleep 10
    
    # Health check
    if curl -f http://localhost:8000/api/health > /dev/null 2>&1; then
        echo "✅ Production environment deployed successfully!"
        echo "📊 Dashboard: http://localhost:8000"
        echo "📚 API Docs: http://localhost:8000/docs"
        echo "🔍 Logs: docker-compose -f docker-compose.prod.yml logs -f"
    else
        echo "❌ Health check failed. Check logs for details."
        docker-compose -f docker-compose.prod.yml logs
        exit 1
    fi
}

# Function to deploy with nginx proxy
deploy_with_proxy() {
    echo "📦 Building production environment with nginx proxy..."
    
    # Stop existing containers
    docker-compose -f docker-compose.prod.yml --profile with-proxy down --remove-orphans
    
    # Build and start services with proxy
    docker-compose -f docker-compose.prod.yml --profile with-proxy up --build -d
    
    # Wait for services to be ready
    echo "⏳ Waiting for services to be ready..."
    sleep 15
    
    # Health check through nginx
    if curl -f http://localhost/api/health > /dev/null 2>&1; then
        echo "✅ Production environment with proxy deployed successfully!"
        echo "📊 Dashboard: http://localhost"
        echo "📚 API Docs: http://localhost/docs"
        echo "🔍 App Logs: docker-compose -f docker-compose.prod.yml logs fastapi-production-tracker"
        echo "🔍 Nginx Logs: docker-compose -f docker-compose.prod.yml logs nginx"
    else
        echo "❌ Health check failed. Check logs for details."
        docker-compose -f docker-compose.prod.yml --profile with-proxy logs
        exit 1
    fi
}

# Function to show status
show_status() {
    echo "📊 Current deployment status:"
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
    
    echo ""
    echo "🔍 Recent logs:"
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml logs --tail=10
    else
        docker-compose logs --tail=10
    fi
}

# Function to stop services
stop_services() {
    echo "🛑 Stopping services..."
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml down
    else
        docker-compose down
    fi
    
    echo "✅ Services stopped successfully!"
}

# Function to clean up
cleanup() {
    echo "🧹 Cleaning up..."
    
    # Remove stopped containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes (be careful with this)
    # docker volume prune -f
    
    echo "✅ Cleanup completed!"
}

# Main deployment logic
case $ENVIRONMENT in
    "dev")
        deploy_dev
        ;;
    "prod")
        deploy_prod
        ;;
    "prod-proxy")
        deploy_with_proxy
        ;;
    "status")
        show_status
        ;;
    "stop")
        stop_services
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "❌ Unknown environment: $ENVIRONMENT"
        echo "Usage: $0 [dev|prod|prod-proxy|status|stop|cleanup]"
        exit 1
        ;;
esac

echo ""
echo "🎉 Deployment completed for environment: $ENVIRONMENT"
echo "💡 Useful commands:"
echo "   - View logs: docker-compose logs -f"
echo "   - Check status: docker-compose ps"
echo "   - Stop services: docker-compose down"
echo "   - Restart: docker-compose restart"
