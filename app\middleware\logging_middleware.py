"""
Logging middleware for FastAPI application.
"""

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging import access_logger, app_logger


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log HTTP requests and responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process the request and log details."""
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        
        # Add request ID to request state
        request.state.request_id = request_id
        
        # Start timing
        start_time = time.time()
        
        # Log request
        self._log_request(request, request_id)
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate response time
            process_time = time.time() - start_time
            
            # Log response
            self._log_response(request, response, request_id, process_time)
            
            # Add response headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(round(process_time * 1000, 2))
            
            return response
            
        except Exception as e:
            # Calculate response time for error
            process_time = time.time() - start_time
            
            # Log error
            self._log_error(request, e, request_id, process_time)
            
            # Re-raise the exception
            raise
    
    def _log_request(self, request: Request, request_id: str) -> None:
        """Log incoming request details."""
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        access_logger.info(
            f"Request started: {request.method} {request.url.path}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "client_ip": client_ip,
                "user_agent": user_agent,
                "headers": dict(request.headers),
            }
        )
    
    def _log_response(self, request: Request, response: Response, 
                     request_id: str, process_time: float) -> None:
        """Log response details."""
        client_ip = self._get_client_ip(request)
        
        access_logger.info(
            f"Request completed: {request.method} {request.url.path} - {response.status_code}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "endpoint": request.url.path,
                "status_code": response.status_code,
                "response_time": round(process_time * 1000, 2),  # ms
                "client_ip": client_ip,
            }
        )
        
        # Log slow requests
        if process_time > 2.0:  # 2 seconds threshold
            app_logger.warning(
                f"Slow request detected: {request.method} {request.url.path}",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "endpoint": request.url.path,
                    "response_time": round(process_time * 1000, 2),
                    "client_ip": client_ip,
                }
            )
    
    def _log_error(self, request: Request, error: Exception, 
                  request_id: str, process_time: float) -> None:
        """Log error details."""
        client_ip = self._get_client_ip(request)
        
        app_logger.error(
            f"Request failed: {request.method} {request.url.path} - {type(error).__name__}: {str(error)}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "endpoint": request.url.path,
                "error_type": type(error).__name__,
                "error_message": str(error),
                "response_time": round(process_time * 1000, 2),
                "client_ip": client_ip,
            },
            exc_info=True
        )
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
