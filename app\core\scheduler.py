"""
Background task scheduler for periodic monitoring and alerts.
"""

import asyncio
from datetime import datetime
from typing import Optional

from app.core.config import get_settings
from app.core.database import get_analyzer
from app.core.logging import app_logger
from app.services.alert_service import alert_service


class BackgroundScheduler:
    """Background task scheduler for periodic monitoring."""
    
    def __init__(self):
        self.settings = get_settings()
        self.running = False
        self.tasks = []
    
    async def start(self):
        """Start the background scheduler."""
        if self.running:
            return
        
        self.running = True
        app_logger.info("Starting background scheduler")
        
        # Start periodic tasks
        if self.settings.enable_alerts:
            alert_task = asyncio.create_task(self._alert_check_loop())
            self.tasks.append(alert_task)
            app_logger.info(f"Alert checking enabled with {self.settings.alert_check_interval}s interval")
        
        # Start health monitoring task
        health_task = asyncio.create_task(self._health_check_loop())
        self.tasks.append(health_task)
        app_logger.info("Health monitoring enabled")
    
    async def stop(self):
        """Stop the background scheduler."""
        if not self.running:
            return
        
        self.running = False
        app_logger.info("Stopping background scheduler")
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        self.tasks.clear()
        app_logger.info("Background scheduler stopped")
    
    async def _alert_check_loop(self):
        """Periodic alert checking loop."""
        while self.running:
            try:
                await self._run_alert_check()
                await asyncio.sleep(self.settings.alert_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                app_logger.error(f"Error in alert check loop: {e}", exc_info=True)
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _health_check_loop(self):
        """Periodic health monitoring loop."""
        while self.running:
            try:
                await self._run_health_check()
                await asyncio.sleep(300)  # Check every 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                app_logger.error(f"Error in health check loop: {e}", exc_info=True)
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _run_alert_check(self):
        """Run alert checking."""
        try:
            # Get analyzer instance
            from app.core.database import _analyzer
            if _analyzer is None:
                app_logger.warning("Analyzer not available for alert check")
                return
            
            app_logger.debug("Running periodic alert check")
            
            # Check production alerts
            alerts = await alert_service.check_production_alerts(_analyzer)
            
            # Process any new alerts
            if alerts:
                await alert_service.process_alerts(alerts)
                app_logger.info(f"Processed {len(alerts)} alerts during periodic check")
            
        except Exception as e:
            app_logger.error(f"Error during alert check: {e}", exc_info=True)
    
    async def _run_health_check(self):
        """Run health monitoring."""
        try:
            from app.core.database import _analyzer
            
            # Check database connectivity
            if _analyzer is None:
                app_logger.warning("Database analyzer not available")
                
                # Create database connection alert
                from app.services.alert_service import Alert, AlertType, AlertSeverity
                
                db_alert = Alert(
                    id=f"db_connection_{datetime.now().timestamp()}",
                    type=AlertType.DATABASE_CONNECTION,
                    severity=AlertSeverity.CRITICAL,
                    title="Database Connection Lost",
                    message="Database analyzer is not available - connection may be lost",
                    timestamp=datetime.now(),
                    data={"check_type": "periodic_health_check"}
                )
                
                await alert_service.process_alerts([db_alert])
                return
            
            # Test database connectivity
            try:
                test_result = _analyzer.execute_query("SELECT 1 as health_check")
                if test_result is None or test_result.empty:
                    raise Exception("Database query returned empty result")
                
                app_logger.debug("Database health check passed")
                
            except Exception as db_error:
                app_logger.error(f"Database health check failed: {db_error}")
                
                # Create database error alert
                from app.services.alert_service import Alert, AlertType, AlertSeverity
                
                db_alert = Alert(
                    id=f"db_error_{datetime.now().timestamp()}",
                    type=AlertType.DATABASE_CONNECTION,
                    severity=AlertSeverity.HIGH,
                    title="Database Health Check Failed",
                    message=f"Database health check failed: {str(db_error)}",
                    timestamp=datetime.now(),
                    data={
                        "check_type": "periodic_health_check",
                        "error": str(db_error)
                    }
                )
                
                await alert_service.process_alerts([db_alert])
            
        except Exception as e:
            app_logger.error(f"Error during health check: {e}", exc_info=True)


# Global scheduler instance
scheduler = BackgroundScheduler()
