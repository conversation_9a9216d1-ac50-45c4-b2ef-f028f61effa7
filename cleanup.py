#!/usr/bin/env python3
"""
Cleanup script to remove non-essential files and optimize the application.
"""

import os
import shutil
import sys
from pathlib import Path
from typing import List, Set


class ApplicationCleanup:
    """Clean up non-essential files and optimize the application."""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.removed_files = []
        self.removed_dirs = []
        self.kept_files = []
        
        # Define essential files and directories
        self.essential_files = {
            # Core application files
            'main.py',
            'requirements.txt',
            'Dockerfile',
            'docker-compose.yml',
            'docker-compose.prod.yml',
            'nginx.conf',
            'deploy.sh',
            '.env.template',
            '.gitignore',
            'README.md',
            
            # Documentation
            'DOCUMENTATION_TECHNIQUE.md',
            'API_ROUTES_DOCUMENTATION.md',
            'DEPLOYMENT_CHECKLIST.md',
            
            # Application directories (will check contents)
            'app/',
            'templates/',
            'static/',
            'logs/',
        }
        
        # Files that can be safely removed
        self.removable_files = {
            # Development/testing files
            'test.py',
            'debug.py',
            'temp.py',
            'scratch.py',
            'example.py',
            'sample.py',
            
            # Backup files
            '*.bak',
            '*.backup',
            '*.old',
            '*.orig',
            '*~',
            
            # IDE files
            '.vscode/',
            '.idea/',
            '*.swp',
            '*.swo',
            
            # OS files
            '.DS_Store',
            'Thumbs.db',
            'desktop.ini',
            
            # Python cache
            '__pycache__/',
            '*.pyc',
            '*.pyo',
            '*.pyd',
            '.pytest_cache/',
            '.coverage',
            'htmlcov/',
            
            # Node.js (if any)
            'node_modules/',
            'package-lock.json',
            'yarn.lock',
            
            # Logs (keep directory, remove old logs)
            'logs/*.log.*',
            'logs/old/',
            
            # Temporary files
            'tmp/',
            'temp/',
            '.tmp/',
        }
        
        # Essential app subdirectories and files
        self.essential_app_structure = {
            'app/__init__.py',
            'app/core/',
            'app/routes/',
            'app/services/',
            'app/models/',
            'app/middleware/',
            'app/controllers/',
        }
    
    def scan_directory(self, directory: Path) -> List[Path]:
        """Scan directory for files and subdirectories."""
        items = []
        try:
            for item in directory.iterdir():
                items.append(item)
        except PermissionError:
            print(f"⚠️  Permission denied: {directory}")
        return items
    
    def is_essential_file(self, file_path: Path) -> bool:
        """Check if a file is essential for the application."""
        relative_path = file_path.relative_to(self.project_root)
        
        # Check if it's in essential files list
        if str(relative_path) in self.essential_files:
            return True
        
        # Check if it's in essential app structure
        if str(relative_path) in self.essential_app_structure:
            return True
        
        # Check if it's in an essential directory
        for essential in self.essential_files:
            if essential.endswith('/') and str(relative_path).startswith(essential):
                return True
        
        # Check specific patterns for essential files
        if relative_path.suffix in ['.py', '.html', '.js', '.css']:
            # Keep Python, HTML, JS, CSS files in app directories
            if any(part in ['app', 'templates', 'static'] for part in relative_path.parts):
                return True
        
        return False
    
    def should_remove_file(self, file_path: Path) -> bool:
        """Check if a file should be removed."""
        relative_path = file_path.relative_to(self.project_root)
        
        # Check against removable patterns
        for pattern in self.removable_files:
            if pattern.endswith('/'):
                # Directory pattern
                if str(relative_path).startswith(pattern) or relative_path.name == pattern.rstrip('/'):
                    return True
            elif '*' in pattern:
                # Wildcard pattern
                import fnmatch
                if fnmatch.fnmatch(str(relative_path), pattern) or fnmatch.fnmatch(relative_path.name, pattern):
                    return True
            else:
                # Exact match
                if str(relative_path) == pattern or relative_path.name == pattern:
                    return True
        
        return False
    
    def clean_python_cache(self):
        """Remove Python cache files and directories."""
        print("🧹 Cleaning Python cache files...")
        
        for root, dirs, files in os.walk(self.project_root):
            # Remove __pycache__ directories
            if '__pycache__' in dirs:
                cache_dir = Path(root) / '__pycache__'
                try:
                    shutil.rmtree(cache_dir)
                    self.removed_dirs.append(str(cache_dir))
                    print(f"   Removed: {cache_dir}")
                except Exception as e:
                    print(f"   ⚠️  Could not remove {cache_dir}: {e}")
            
            # Remove .pyc files
            for file in files:
                if file.endswith(('.pyc', '.pyo', '.pyd')):
                    file_path = Path(root) / file
                    try:
                        file_path.unlink()
                        self.removed_files.append(str(file_path))
                        print(f"   Removed: {file_path}")
                    except Exception as e:
                        print(f"   ⚠️  Could not remove {file_path}: {e}")
    
    def clean_logs(self):
        """Clean old log files but keep the logs directory."""
        print("🧹 Cleaning old log files...")
        
        logs_dir = self.project_root / 'logs'
        if logs_dir.exists():
            for log_file in logs_dir.glob('*.log.*'):
                try:
                    log_file.unlink()
                    self.removed_files.append(str(log_file))
                    print(f"   Removed: {log_file}")
                except Exception as e:
                    print(f"   ⚠️  Could not remove {log_file}: {e}")
    
    def clean_removable_files(self):
        """Remove files that match removable patterns."""
        print("🧹 Removing non-essential files...")
        
        for root, dirs, files in os.walk(self.project_root):
            root_path = Path(root)
            
            # Check directories
            for dir_name in dirs[:]:  # Use slice to avoid modification during iteration
                dir_path = root_path / dir_name
                if self.should_remove_file(dir_path) and not self.is_essential_file(dir_path):
                    try:
                        shutil.rmtree(dir_path)
                        self.removed_dirs.append(str(dir_path))
                        print(f"   Removed directory: {dir_path}")
                        dirs.remove(dir_name)  # Don't recurse into removed directory
                    except Exception as e:
                        print(f"   ⚠️  Could not remove directory {dir_path}: {e}")
            
            # Check files
            for file_name in files:
                file_path = root_path / file_name
                if self.should_remove_file(file_path) and not self.is_essential_file(file_path):
                    try:
                        file_path.unlink()
                        self.removed_files.append(str(file_path))
                        print(f"   Removed: {file_path}")
                    except Exception as e:
                        print(f"   ⚠️  Could not remove {file_path}: {e}")
    
    def optimize_docker_files(self):
        """Optimize Docker-related files."""
        print("🐳 Optimizing Docker configuration...")
        
        # Check if .dockerignore exists, create if not
        dockerignore_path = self.project_root / '.dockerignore'
        if not dockerignore_path.exists():
            dockerignore_content = """
# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.pytest_cache/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation (not needed in container)
*.md
docs/

# Development files
.env
docker-compose.yml
deploy.sh
cleanup.py

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
"""
            try:
                dockerignore_path.write_text(dockerignore_content.strip())
                print(f"   Created: {dockerignore_path}")
            except Exception as e:
                print(f"   ⚠️  Could not create .dockerignore: {e}")
    
    def generate_report(self):
        """Generate cleanup report."""
        print("\n" + "="*60)
        print("🎉 CLEANUP COMPLETED")
        print("="*60)
        
        print(f"📁 Removed {len(self.removed_dirs)} directories:")
        for dir_path in self.removed_dirs:
            print(f"   - {dir_path}")
        
        print(f"\n📄 Removed {len(self.removed_files)} files:")
        for file_path in self.removed_files:
            print(f"   - {file_path}")
        
        # Calculate space saved (approximate)
        total_removed = len(self.removed_files) + len(self.removed_dirs)
        print(f"\n📊 Total items removed: {total_removed}")
        
        print("\n✅ Application is now optimized for production!")
        print("💡 Next steps:")
        print("   1. Test the application: python main.py")
        print("   2. Build Docker image: docker build -t production-tracker .")
        print("   3. Deploy: ./deploy.sh prod")
    
    def run_cleanup(self, dry_run: bool = False):
        """Run the complete cleanup process."""
        if dry_run:
            print("🔍 DRY RUN MODE - No files will be actually removed")
        
        print("🚀 Starting application cleanup...")
        print(f"📂 Project root: {self.project_root}")
        
        if not dry_run:
            self.clean_python_cache()
            self.clean_logs()
            self.clean_removable_files()
            self.optimize_docker_files()
            self.generate_report()
        else:
            print("🔍 Dry run completed - use --execute to perform actual cleanup")


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Clean up non-essential files")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be removed without actually removing")
    parser.add_argument("--execute", action="store_true", help="Actually perform the cleanup")
    
    args = parser.parse_args()
    
    if not args.execute and not args.dry_run:
        print("⚠️  Please specify either --dry-run or --execute")
        print("   --dry-run: Show what would be removed")
        print("   --execute: Actually perform cleanup")
        sys.exit(1)
    
    cleanup = ApplicationCleanup()
    cleanup.run_cleanup(dry_run=args.dry_run)


if __name__ == "__main__":
    main()
