"""
Alert service for production monitoring and notifications.
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from app.core.logging import alert_logger, app_logger
from app.core.data_analyzer import ExcaliburDataAnalyzer


class AlertSeverity(Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertType(Enum):
    """Types of alerts."""
    PRODUCTION_DELAY = "production_delay"
    SYSTEM_ERROR = "system_error"
    DATABASE_CONNECTION = "database_connection"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    THRESHOLD_EXCEEDED = "threshold_exceeded"


@dataclass
class Alert:
    """Alert data structure."""
    id: str
    type: AlertType
    severity: AlertSeverity
    title: str
    message: str
    timestamp: datetime
    data: Dict[str, Any]
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class AlertService:
    """Service for managing production alerts."""
    
    def __init__(self):
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.alert_rules = self._initialize_alert_rules()
    
    def _initialize_alert_rules(self) -> Dict[str, Dict[str, Any]]:
        """Initialize alert rules and thresholds."""
        return {
            "production_delay": {
                "threshold_hours": 24,  # Alert if OF is delayed by more than 24 hours
                "severity": AlertSeverity.HIGH,
                "enabled": True
            },
            "database_connection": {
                "threshold_failures": 3,  # Alert after 3 consecutive failures
                "severity": AlertSeverity.CRITICAL,
                "enabled": True
            },
            "response_time": {
                "threshold_ms": 5000,  # Alert if response time > 5 seconds
                "severity": AlertSeverity.MEDIUM,
                "enabled": True
            },
            "error_rate": {
                "threshold_percent": 10,  # Alert if error rate > 10%
                "severity": AlertSeverity.HIGH,
                "enabled": True
            }
        }
    
    async def check_production_alerts(self, analyzer: ExcaliburDataAnalyzer) -> List[Alert]:
        """Check for production-related alerts."""
        alerts = []
        
        try:
            # Get current production data
            current_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            
            of_data = analyzer.get_comprehensive_of_data(start_date, current_date, 'C')
            
            if of_data is not None and not of_data.empty:
                # Check for delayed production orders
                delayed_alerts = self._check_production_delays(of_data)
                alerts.extend(delayed_alerts)
                
                # Check for efficiency issues
                efficiency_alerts = self._check_efficiency_issues(of_data)
                alerts.extend(efficiency_alerts)
            
        except Exception as e:
            alert_logger.error(f"Error checking production alerts: {e}", exc_info=True)
            
            # Create system error alert
            system_alert = Alert(
                id=f"system_error_{datetime.now().timestamp()}",
                type=AlertType.SYSTEM_ERROR,
                severity=AlertSeverity.HIGH,
                title="Production Data Check Failed",
                message=f"Failed to check production data: {str(e)}",
                timestamp=datetime.now(),
                data={"error": str(e)}
            )
            alerts.append(system_alert)
        
        return alerts
    
    def _check_production_delays(self, of_data) -> List[Alert]:
        """Check for production delays."""
        alerts = []
        threshold_hours = self.alert_rules["production_delay"]["threshold_hours"]
        
        try:
            current_time = datetime.now()
            
            for _, row in of_data.iterrows():
                # Check if there's a deadline and if it's exceeded
                if 'LANCEMENT_AU_PLUS_TARD' in row and row['LANCEMENT_AU_PLUS_TARD']:
                    try:
                        deadline = datetime.strptime(str(row['LANCEMENT_AU_PLUS_TARD']), '%Y-%m-%d')
                        delay_hours = (current_time - deadline).total_seconds() / 3600
                        
                        if delay_hours > threshold_hours:
                            alert = Alert(
                                id=f"delay_{row.get('NUMERO_OFDA', 'unknown')}_{current_time.timestamp()}",
                                type=AlertType.PRODUCTION_DELAY,
                                severity=AlertSeverity.HIGH,
                                title=f"Production Delay: {row.get('NUMERO_OFDA', 'Unknown OF')}",
                                message=f"OF {row.get('NUMERO_OFDA')} is delayed by {delay_hours:.1f} hours",
                                timestamp=current_time,
                                data={
                                    "of_number": row.get('NUMERO_OFDA'),
                                    "product": row.get('PRODUIT'),
                                    "client": row.get('CLIENT'),
                                    "delay_hours": delay_hours,
                                    "deadline": str(deadline)
                                }
                            )
                            alerts.append(alert)
                    except (ValueError, TypeError):
                        continue
                        
        except Exception as e:
            alert_logger.error(f"Error checking production delays: {e}", exc_info=True)
        
        return alerts
    
    def _check_efficiency_issues(self, of_data) -> List[Alert]:
        """Check for efficiency issues."""
        alerts = []
        
        try:
            # Calculate overall efficiency metrics
            total_records = len(of_data)
            if total_records == 0:
                return alerts
            
            # Check completion rate
            completed_count = len(of_data[of_data.get('STATUT') == 'T'])
            completion_rate = completed_count / total_records
            
            if completion_rate < 0.7:  # Less than 70% completion rate
                alert = Alert(
                    id=f"efficiency_low_{datetime.now().timestamp()}",
                    type=AlertType.THRESHOLD_EXCEEDED,
                    severity=AlertSeverity.MEDIUM,
                    title="Low Production Efficiency",
                    message=f"Production completion rate is {completion_rate:.1%}, below expected threshold",
                    timestamp=datetime.now(),
                    data={
                        "completion_rate": completion_rate,
                        "total_orders": total_records,
                        "completed_orders": completed_count
                    }
                )
                alerts.append(alert)
                
        except Exception as e:
            alert_logger.error(f"Error checking efficiency issues: {e}", exc_info=True)
        
        return alerts
    
    async def process_alerts(self, alerts: List[Alert]) -> None:
        """Process and store alerts."""
        for alert in alerts:
            # Check if this alert already exists
            if alert.id not in self.active_alerts:
                self.active_alerts[alert.id] = alert
                self.alert_history.append(alert)
                
                # Log the alert
                alert_logger.warning(
                    f"New alert: {alert.title}",
                    extra={
                        "alert_id": alert.id,
                        "alert_type": alert.type.value,
                        "severity": alert.severity.value,
                        "message": alert.message,
                        "data": alert.data
                    }
                )
                
                # Send notifications (email, etc.)
                await self._send_alert_notification(alert)
    
    async def _send_alert_notification(self, alert: Alert) -> None:
        """Send alert notification via configured channels."""
        try:
            # Import here to avoid circular imports
            from app.services.email_service import EmailService
            
            email_service = EmailService()
            await email_service.send_alert_email(alert)
            
        except Exception as e:
            app_logger.error(f"Failed to send alert notification: {e}", exc_info=True)
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts."""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """Get alert history."""
        return self.alert_history[-limit:]
    
    def resolve_alert(self, alert_id: str) -> bool:
        """Resolve an active alert."""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolved_at = datetime.now()
            
            # Remove from active alerts
            del self.active_alerts[alert_id]
            
            alert_logger.info(f"Alert resolved: {alert.title}", extra={"alert_id": alert_id})
            return True
        
        return False


# Global alert service instance
alert_service = AlertService()
