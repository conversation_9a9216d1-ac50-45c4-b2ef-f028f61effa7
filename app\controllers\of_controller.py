"""
OF (Orders of Fabrication) Controller - Business logic for OF operations
"""

from typing import Optional, Dict, Any, List
import pandas as pd
from app.core.database import get_analyzer


class OFController:
    """Controller for OF business logic"""

    def __init__(self):
        pass
    
    def get_all_of(self, analyzer, limit: Optional[int] = None) -> Dict[str, Any]:
        """Get all OF data"""
        try:
            of_data = analyzer.get_of_data()

            if limit and not of_data.empty:
                of_data = of_data.head(limit)

            return {
                "of_list": of_data.to_dict('records') if not of_data.empty else [],
                "count": len(of_data)
            }

        except Exception as e:
            raise Exception(f"Error getting all OF: {str(e)}")

    def get_of_en_cours(self, analyzer) -> Dict[str, Any]:
        """Get OF currently in progress"""
        try:
            of_data = analyzer.get_of_data(statut_filter='C')

            return {
                "of_list": of_data.to_dict('records') if not of_data.empty else [],
                "count": len(of_data)
            }

        except Exception as e:
            raise Exception(f"Error getting OF en cours: {str(e)}")

    def get_of_histo(self, analyzer) -> Dict[str, Any]:
        """Get historical OF data (completed and stopped)"""
        try:
            # Get completed and stopped OF
            of_termines = analyzer.get_of_data(statut_filter='T')
            of_arretes = analyzer.get_of_data(statut_filter='A')

            # Combine the data
            if not of_termines.empty and not of_arretes.empty:
                of_data = pd.concat([of_termines, of_arretes], ignore_index=True)
            elif not of_termines.empty:
                of_data = of_termines
            elif not of_arretes.empty:
                of_data = of_arretes
            else:
                of_data = pd.DataFrame()

            # Sort by date if available
            if not of_data.empty and 'LANCEMENT_AU_PLUS_TARD' in of_data.columns:
                of_data = of_data.sort_values('LANCEMENT_AU_PLUS_TARD', ascending=False)

            return {
                "of_list": of_data.to_dict('records') if not of_data.empty else [],
                "count": len(of_data)
            }

        except Exception as e:
            raise Exception(f"Error getting OF historique: {str(e)}")
    
    def get_of_by_status(self, analyzer, status: str) -> Dict[str, Any]:
        """Get OF by specific status"""
        try:
            # Validate status
            valid_statuses = ['C', 'T', 'A']
            if status not in valid_statuses:
                raise ValueError(f"Invalid status. Must be one of: {valid_statuses}")

            of_data = analyzer.get_of_data(statut_filter=status)

            return {
                "of_list": of_data.to_dict('records') if not of_data.empty else [],
                "count": len(of_data),
                "status": status
            }

        except Exception as e:
            raise Exception(f"Error getting OF by status {status}: {str(e)}")

    def get_of_with_filters(
        self,
        analyzer,
        date_debut: Optional[str] = None,
        date_fin: Optional[str] = None,
        statut_filter: Optional[str] = None,
        famille_filter: Optional[str] = None,
        client_filter: Optional[str] = None,
        alerte_filter: Optional[bool] = None,
        limit: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get OF data with comprehensive filters"""
        try:
            of_data = analyzer.get_of_data(
                date_debut=date_debut,
                date_fin=date_fin,
                statut_filter=statut_filter,
                famille_filter=famille_filter,
                client_filter=client_filter,
                alerte_filter=alerte_filter
            )

            if limit and not of_data.empty:
                of_data = of_data.head(limit)

            return {
                "of_list": of_data.to_dict('records') if not of_data.empty else [],
                "count": len(of_data),
                "filters_applied": {
                    "date_debut": date_debut,
                    "date_fin": date_fin,
                    "statut_filter": statut_filter,
                    "famille_filter": famille_filter,
                    "client_filter": client_filter,
                    "alerte_filter": alerte_filter,
                    "limit": limit
                }
            }

        except Exception as e:
            raise Exception(f"Error getting filtered OF: {str(e)}")

    def get_of_statistics(self, analyzer) -> Dict[str, Any]:
        """Get OF statistics summary"""
        try:
            of_data = analyzer.get_of_data()

            if of_data.empty:
                return {
                    "total": 0,
                    "by_status": {"C": 0, "T": 0, "A": 0},
                    "avg_advancement": {"production": 0.0, "time": 0.0},
                    "alerts_count": 0
                }

            # Count by status
            status_counts = of_data['STATUT'].value_counts().to_dict()
            by_status = {
                "C": status_counts.get('C', 0),
                "T": status_counts.get('T', 0),
                "A": status_counts.get('A', 0)
            }

            # Calculate averages
            avg_prod = of_data['Avancement_PROD'].mean() * 100 if 'Avancement_PROD' in of_data.columns else 0.0
            avg_temps = of_data['Avancement_temps'].mean() * 100 if 'Avancement_temps' in of_data.columns else 0.0

            # Count alerts
            alerts_count = len(of_data[of_data.get('Alerte_temps', False) == True])

            return {
                "total": len(of_data),
                "by_status": by_status,
                "avg_advancement": {
                    "production": round(avg_prod, 1),
                    "time": round(avg_temps, 1)
                },
                "alerts_count": alerts_count
            }

        except Exception as e:
            raise Exception(f"Error getting OF statistics: {str(e)}")
