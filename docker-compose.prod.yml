version: '3.8'

services:
  fastapi-production-tracker:
    build: 
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DB_UID=${DB_UID}
      - DB_PWD=${DB_PWD}
      - DB_HOST=${DB_HOST}
      - DB_SERVER_NAME=${DB_SERVER_NAME}
      - DB_DATABASE_NAME=${DB_DATABASE_NAME}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - SMTP_HOST=${SMTP_HOST:-}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USER=${SMTP_USER:-}
      - SMTP_PASSWORD=${SMTP_PASSWORD:-}
      - ALERT_EMAIL_RECIPIENTS=${ALERT_EMAIL_RECIPIENTS:-}
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - production-tracker-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - fastapi-production-tracker
    restart: unless-stopped
    networks:
      - production-tracker-network
    profiles:
      - with-proxy

networks:
  production-tracker-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
