"""
Alert management routes.
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import List, Optional
from datetime import datetime

from app.core.database import get_analyzer
from app.models.schemas import BaseResponse
from app.services.alert_service import alert_service, Alert
from app.core.logging import app_logger

router = APIRouter(prefix="/api/alerts", tags=["Alerts"])


@router.get("/", response_model=BaseResponse)
async def get_active_alerts():
    """Get all active alerts."""
    try:
        alerts = alert_service.get_active_alerts()
        
        # Convert alerts to dict format for JSON serialization
        alerts_data = []
        for alert in alerts:
            alerts_data.append({
                "id": alert.id,
                "type": alert.type.value,
                "severity": alert.severity.value,
                "title": alert.title,
                "message": alert.message,
                "timestamp": alert.timestamp.isoformat(),
                "data": alert.data,
                "resolved": alert.resolved,
                "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None
            })
        
        return BaseResponse(success=True, data={
            "alerts": alerts_data,
            "count": len(alerts_data)
        })
    except Exception as e:
        app_logger.error(f"Error fetching active alerts: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error fetching alerts: {str(e)}")


@router.get("/history", response_model=BaseResponse)
async def get_alert_history(limit: int = 100):
    """Get alert history."""
    try:
        alerts = alert_service.get_alert_history(limit=limit)
        
        # Convert alerts to dict format for JSON serialization
        alerts_data = []
        for alert in alerts:
            alerts_data.append({
                "id": alert.id,
                "type": alert.type.value,
                "severity": alert.severity.value,
                "title": alert.title,
                "message": alert.message,
                "timestamp": alert.timestamp.isoformat(),
                "data": alert.data,
                "resolved": alert.resolved,
                "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None
            })
        
        return BaseResponse(success=True, data={
            "alerts": alerts_data,
            "count": len(alerts_data),
            "limit": limit
        })
    except Exception as e:
        app_logger.error(f"Error fetching alert history: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error fetching alert history: {str(e)}")


@router.post("/check", response_model=BaseResponse)
async def check_alerts(background_tasks: BackgroundTasks, analyzer=Depends(get_analyzer)):
    """Manually trigger alert checking."""
    try:
        # Run alert checking in background
        background_tasks.add_task(run_alert_check, analyzer)
        
        return BaseResponse(success=True, data={
            "message": "Alert check initiated",
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        app_logger.error(f"Error initiating alert check: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error initiating alert check: {str(e)}")


@router.post("/{alert_id}/resolve", response_model=BaseResponse)
async def resolve_alert(alert_id: str):
    """Resolve an active alert."""
    try:
        success = alert_service.resolve_alert(alert_id)
        
        if success:
            return BaseResponse(success=True, data={
                "message": f"Alert {alert_id} resolved successfully",
                "alert_id": alert_id,
                "resolved_at": datetime.now().isoformat()
            })
        else:
            raise HTTPException(status_code=404, detail=f"Alert {alert_id} not found")
            
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error resolving alert {alert_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error resolving alert: {str(e)}")


@router.get("/stats", response_model=BaseResponse)
async def get_alert_stats():
    """Get alert statistics."""
    try:
        active_alerts = alert_service.get_active_alerts()
        alert_history = alert_service.get_alert_history(limit=1000)
        
        # Calculate statistics
        stats = {
            "active_count": len(active_alerts),
            "total_count": len(alert_history),
            "by_severity": {},
            "by_type": {},
            "recent_24h": 0
        }
        
        # Count by severity and type
        for alert in alert_history:
            severity = alert.severity.value
            alert_type = alert.type.value
            
            stats["by_severity"][severity] = stats["by_severity"].get(severity, 0) + 1
            stats["by_type"][alert_type] = stats["by_type"].get(alert_type, 0) + 1
            
            # Count recent alerts (last 24 hours)
            if (datetime.now() - alert.timestamp).total_seconds() < 86400:
                stats["recent_24h"] += 1
        
        return BaseResponse(success=True, data=stats)
        
    except Exception as e:
        app_logger.error(f"Error fetching alert stats: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error fetching alert stats: {str(e)}")


async def run_alert_check(analyzer):
    """Background task to run alert checking."""
    try:
        app_logger.info("Running manual alert check")
        
        # Check production alerts
        alerts = await alert_service.check_production_alerts(analyzer)
        
        # Process any new alerts
        if alerts:
            await alert_service.process_alerts(alerts)
            app_logger.info(f"Processed {len(alerts)} alerts")
        else:
            app_logger.info("No new alerts found")
            
    except Exception as e:
        app_logger.error(f"Error in background alert check: {e}", exc_info=True)


@router.get("/test", response_model=BaseResponse)
async def test_alert_system():
    """Test the alert system by creating a test alert."""
    try:
        from app.services.alert_service import Alert, AlertType, AlertSeverity
        
        # Create a test alert
        test_alert = Alert(
            id=f"test_alert_{datetime.now().timestamp()}",
            type=AlertType.SYSTEM_ERROR,
            severity=AlertSeverity.LOW,
            title="Test Alert",
            message="This is a test alert to verify the alert system is working",
            timestamp=datetime.now(),
            data={"test": True, "generated_by": "test_endpoint"}
        )
        
        # Process the test alert
        await alert_service.process_alerts([test_alert])
        
        return BaseResponse(success=True, data={
            "message": "Test alert created successfully",
            "alert_id": test_alert.id,
            "timestamp": test_alert.timestamp.isoformat()
        })
        
    except Exception as e:
        app_logger.error(f"Error creating test alert: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error creating test alert: {str(e)}")
